import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../Model/CustomerModel.dart';
import '../../Model/LoanDetail.dart';
import '../../Provider/loan_provider.dart';
import '../../Utils/amount_formatter.dart';
import '../../Widgets/interest_amount_display.dart';
import '../EntryDetails/entry_details_screen.dart';

class LoanDetail extends StatefulWidget {
  final Loandetail detail;
  final Customer customer;

  const LoanDetail({
    super.key,
    required this.detail,
    required this.customer,
  });

  @override
  State<LoanDetail> createState() => LoanDetailState();
}

class LoanDetailState extends State<LoanDetail> {
  String formatDate(String date) {
    try {
      DateTime parsedDate = DateTime.parse(date);
      return DateFormat('dd-MM-yyyy hh:mm a').format(parsedDate);
    } catch (e) {
      return date;
    }
  }

  String formatFullDate(String date) {
    try {
      DateTime parsedDate = DateTime.parse(date);
      return DateFormat('dd MMMM yyyy').format(parsedDate).toUpperCase();
    } catch (e) {
      return "Invalid Date";
    }
  }

  String formatAmount(double value) {
    return AmountFormatter.formatAmount(value);
  }

  String _getFormattedDate(String dateStr) {
    try {
      final date = DateTime.parse(dateStr);
      return '${date.day} ${_getMonthAbbr(date.month)} ${date.year}';
    } catch (e) {
      return dateStr;
    }
  }

  String _getMonthAbbr(int month) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[month - 1];
  }

  String _calculateMonths() {
    try {
      final startDate = DateTime.parse(widget.detail.startDate);
      final currentDate = DateTime.now();
      final difference = currentDate.difference(startDate);
      final months = (difference.inDays / 30).round();
      return '$months Months';
    } catch (e) {
      return '0 Months';
    }
  }

  @override
  Widget build(BuildContext context) {
    final totalInterest = double.tryParse(widget.detail.totalInterest) ?? 0;
    final loanAmount = double.tryParse(widget.detail.amount) ?? 0;
    final totalDeposit = double.tryParse(widget.detail.totalDeposite) ?? 0;
    final remainingAmount = loanAmount - totalDeposit;
    final interestRate = double.tryParse(widget.detail.rate) ?? 0;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.blueGrey.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blueGrey.withValues(alpha: 0.15),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.8),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
        border: Border.all(
          color: Colors.blueGrey.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () async {
            // Store context reference before async operations
            final navigator = Navigator.of(context);
            final scaffoldMessenger = ScaffoldMessenger.of(context);

            // Navigate to entry details
            await navigator.push(
              MaterialPageRoute(
                builder: (context) => EntryDetailsScreen(
                  loanDetail: widget.detail,
                  customer: widget.customer,
                ),
              ),
            );

            // Refresh loan data when returning from entry details
            if (mounted) {
              try {
                final prefs = await SharedPreferences.getInstance();
                final userId = prefs.getString("userId");
                if (userId != null && mounted) {
                  final loanProvider = Provider.of<LoanProvider>(context, listen: false);
                  await loanProvider.fetchLoanDetailListSimple(userId, widget.customer.custId);
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('Failed to refresh loan data: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            }
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header Section
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title Row
                    Row(
                      children: [
                        // Status Indicator
                        Container(
                          width: 4,
                          height: 50,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.blueGrey.shade400,
                                Colors.blueGrey.shade600,
                              ],
                            ),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                        const SizedBox(width: 16),

                        // Loan Title
                        Expanded(
                          child: Row(
                            children: [
                              Icon(
                                Icons.account_balance_wallet_outlined,
                                color: Colors.blueGrey.shade600,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  widget.detail.note.isEmpty ? 'Personal Loan' : widget.detail.note,
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w700,
                                    color: Colors.blueGrey.shade800,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(width: 12),

                        // Loan Type Badge (You Gave/You Got)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: widget.detail.type == '1'
                                ? [Colors.red.shade100, Colors.red.shade200]
                                : [Colors.green.shade100, Colors.green.shade200],
                            ),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: widget.detail.type == '1'
                                ? Colors.red.shade300
                                : Colors.green.shade300,
                              width: 1,
                            ),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                widget.detail.type == '1'
                                  ? Icons.arrow_upward
                                  : Icons.arrow_downward,
                                size: 16,
                                color: widget.detail.type == '1'
                                  ? Colors.red.shade700
                                  : Colors.green.shade700,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                widget.detail.type == '1' ? 'You Gave' : 'You Got',
                                style: TextStyle(
                                  fontSize: 11,
                                  color: widget.detail.type == '1'
                                    ? Colors.red.shade700
                                    : Colors.green.shade700,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(width: 8),

                        // Duration Badge
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.blueGrey.shade100,
                                Colors.blueGrey.shade200,
                              ],
                            ),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: Colors.blueGrey.shade300,
                              width: 1,
                            ),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 16,
                                color: Colors.blueGrey.shade700,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                _calculateMonths(),
                                style: TextStyle(
                                  fontSize: 11,
                                  color: Colors.blueGrey.shade700,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Info Row
                    Padding(
                      padding: const EdgeInsets.only(left: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.schedule,
                                color: Colors.blueGrey.shade500,
                                size: 14,
                              ),
                              const SizedBox(width: 6),
                              Expanded(
                                child: Text(
                                  'Started on ${_getFormattedDate(widget.detail.startDate)}',
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.blueGrey.shade600,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                Icons.trending_up,
                                color: Colors.blueGrey.shade500,
                                size: 14,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                '${AmountFormatter.formatPercentage(interestRate)} Interest Rate',
                                style: TextStyle(
                                  fontSize: 13,
                                  color: Colors.blueGrey.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                widget.detail.paymentMode == 'online'
                                  ? Icons.credit_card
                                  : Icons.money,
                                color: Colors.blueGrey.shade500,
                                size: 14,
                              ),
                              const SizedBox(width: 6),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                decoration: BoxDecoration(
                                  color: widget.detail.paymentMode == 'online'
                                    ? Colors.blue.shade50
                                    : Colors.green.shade50,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: widget.detail.paymentMode == 'online'
                                      ? Colors.blue.shade200
                                      : Colors.green.shade200,
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  widget.detail.paymentMode == 'online' ? 'Online' : 'Cash',
                                  style: TextStyle(
                                    fontSize: 11,
                                    color: widget.detail.paymentMode == 'online'
                                      ? Colors.blue.shade700
                                      : Colors.green.shade700,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Financial Information Cards
                Row(
                  children: [
                    // Principal Amount Card
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.blueGrey.shade50,
                              Colors.blueGrey.shade100,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.blueGrey.shade200,
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.currency_rupee,
                                  color: Colors.blueGrey.shade600,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Principal',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.blueGrey.shade600,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              AmountFormatter.formatCurrency(loanAmount),
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w800,
                                color: Colors.blueGrey.shade800,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (totalDeposit > 0) ...[
                              const SizedBox(height: 4),
                              Text(
                                'Remaining: ${AmountFormatter.formatCurrency(remainingAmount)}',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.blueGrey.shade600,
                                  fontWeight: FontWeight.w500,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // Interest Card
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.orange.shade50,
                              Colors.orange.shade100,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.orange.shade200,
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.trending_up,
                                  color: Colors.orange.shade700,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Interest',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.orange.shade700,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            LayoutBuilder(
                              builder: (context, constraints) {
                                // Adjust font size based on available width
                                double fontSize = constraints.maxWidth < 120 ? 14 : 16;
                                return InterestAmountDisplay(
                                  totalInterest: totalInterest,
                                  fontSize: fontSize,
                                  fontWeight: FontWeight.w800,
                                  showIcon: true,
                                );
                              },
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Up to ${_getFormattedDate(widget.detail.lastInterestUpdatedAt ?? widget.detail.startDate)}',
                              style: TextStyle(
                                fontSize: 9,
                                color: Colors.orange.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

                // Total Due Section
                if (totalInterest > 0) ...[
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: [
                          Colors.blueGrey.shade700,
                          Colors.blueGrey.shade600,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Total Amount Due',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.white.withValues(alpha: 0.9),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                AmountFormatter.formatCurrencyWithDecimals(remainingAmount + totalInterest),
                                style: const TextStyle(
                                  fontSize: 22,
                                  fontWeight: FontWeight.w900,
                                  color: Colors.white,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.arrow_forward_ios,
                          color: Colors.white.withValues(alpha: 0.7),
                          size: 20,
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
