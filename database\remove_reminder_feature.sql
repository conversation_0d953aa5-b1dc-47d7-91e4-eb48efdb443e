-- ========================================
-- Remove Reminder Feature from Database
-- ========================================
-- This script removes all reminder and notification related tables,
-- events, and data from the database.

-- Drop database events first
DROP EVENT IF EXISTS `generate_payment_reminders`;
DROP EVENT IF EXISTS `cleanup_old_notifications`;

-- Drop foreign key constraints first
ALTER TABLE `notifications` DROP FOREIGN KEY IF EXISTS `fk_notifications_reminder`;
ALTER TABLE `notifications` DROP FOREIGN KEY IF EXISTS `fk_notifications_customer`;
ALTER TABLE `notifications` DROP FOREIGN KEY IF EXISTS `fk_notifications_loan`;
ALTER TABLE `notifications` DROP FOREIGN KEY IF EXISTS `fk_notifications_user`;

ALTER TABLE `reminders` DROP FOREIGN KEY IF EXISTS `fk_reminders_customer`;
ALTER TABLE `reminders` DROP FOREIGN KEY IF EXISTS `fk_reminders_loan`;
ALTER TABLE `reminders` DROP FOREIGN KEY IF EXISTS `fk_reminders_user`;

-- Drop tables
DROP TABLE IF EXISTS `notifications`;
DROP TABLE IF EXISTS `reminders`;

-- Update sms_log table to remove reminder message type
ALTER TABLE `sms_log` MODIFY COLUMN `messageType` enum('overdue','custom') NOT NULL DEFAULT 'custom';

-- Clean up any existing reminder-related SMS logs
UPDATE `sms_log` SET `messageType` = 'custom' WHERE `messageType` = 'reminder';

SELECT 'Reminder feature successfully removed from database' as status;
